from rest_framework import serializers
from core.serializers.primary_fields import (
    CompanyBranchPkRelatedField,
    CompanyPaymentMethodPkRelatedField,
    CompanySaleChannelPkRelatedField,
)
from order.models import Order
from report.api_views.sales.serializers import (
    COMPARE_ORDER_LAYOUTS,
    OrderCompareFilterSerializer,
)
from report.permissions import (
    ReportAnalyticDailyCustomerPermission,
    ReportPurchaseFrequencyPermission,
)
from report.report_framework.interface import ReportHeader
from report.report_framework.serializers import FilterSerializer
from report.report_framework.utils import get_datetime_range
from report.report_framework.viewsets import Colors, ReportViewSet
from report.report_framework.serializer_fields import (
    VIEW_BY_DAY,
    CharField,
    ViewByField,
)
from util.report import get_generate_date_range, get_label_for_range_view
from util.serializer_fields import DateTimeRangeField
from datetime import datetime
from django.db.models import Count, Q, F


class CustomerFilter(FilterSerializer):
    search = None
    order_datetime = DateTimeRangeField(
        required=False,
        label="ช่วงวันที่",
        default=get_datetime_range("this_month"),
    )

    _customer_range_view = ViewByField(
        required=False,
        default=VIEW_BY_DAY,
        date_only=True,
        label="ดูข้อมูลตาม",
    )

    branch__in = CompanyBranchPkRelatedField(
        many=True,
        required=False,
        label="สาขา",
    )

    sale_channel__in = CompanySaleChannelPkRelatedField(
        many=True, required=False, label="ช่องทางการขาย"
    )
    payment_method__in = CompanyPaymentMethodPkRelatedField(
        many=True, required=False, label="ช่องทางการชำระเงิน"
    )


class AnalyticDailyCustomer(ReportViewSet):
    permission_classes = [ReportAnalyticDailyCustomerPermission]
    queryset = Order.objects.all()
    filter_serializer = CustomerFilter

    report_headers = [
        ReportHeader(
            title="วันที่",
            key="date",
        ),
        ReportHeader(
            title="จำนวนลูกค้า",
            key="total_customer",
            type="number",
        ),
        ReportHeader(
            title="ลูกค้าใหม่",
            key="total_new_member_customer",
            type="number",
        ),
        ReportHeader(
            title="ลูกค้าเก่า",
            key="total_old_member_customer",
            type="number",
        ),
        ReportHeader(
            title="ลูกค้าไม่มีสมาชิก",
            key="total_no_member_customer",
            type="number",
        ),
        ReportHeader(
            title="จำนวนออเดอร์",
            key="total_order",
            type="number",
        ),
    ]

    def get_report_logic(self, queryset, context):
        filter = context.get("filter", {})
        range_view = filter.get("_customer_range_view", "Day")
        order_datetime = filter.get("order_datetime")
        branches = filter.get("branch__in", [])

        if not order_datetime:
            return []

        start_date = order_datetime.get("lower").date()
        end_date = order_datetime.get("upper").date()

        split_date, date_range = get_generate_date_range(
            range_view, start_date, end_date
        )

        orders = queryset.exclude(order_status=Order.CANCELLED).filter(
            order_datetime__range=(
                order_datetime.get("lower"),
                order_datetime.get("upper"),
            )
        )
        condition = []
        if branches:
            condition.append(Q(order__branch__in=branches))

        total_orders = (
            orders.annotate(order_date=split_date("order_datetime"))
            .values("order_date")
            .annotate(
                total_order=Count("id"),
                total_customer=Count("customer", distinct=True),
                total_no_member=Count(
                    "id",
                    filter=Q(customer__isnull=True),
                    distinct=True,
                ),
                total_new_member_customer=Count(
                    "customer",
                    filter=Q(customer__create_at__gte=(F("order_date"))),
                    distinct=True,
                ),
                total_old_member_customer=Count(
                    "customer",
                    filter=Q(customer__create_at__lt=F("order_date")),
                    distinct=True,
                ),
            )
        )

        report = self.merge_results(
            total_orders,
            date_range,
            range_view,
        )

        return report

    def merge_results(self, total_orders, date_range, range_view):
        report = []

        for order in total_orders:
            date_label = get_label_for_range_view(order["order_date"], range_view)

            if isinstance(order["order_date"], datetime):
                order["order_date"] = order["order_date"].date()

            report_item = {
                "ordering": order["order_date"],
                "date": date_label,
                "total_order": order["total_order"],
                "total_customer": order["total_customer"] + order["total_no_member"],
                "total_new_member_customer": order["total_new_member_customer"],
                "total_old_member_customer": order["total_old_member_customer"],
                "total_no_member_customer": order["total_no_member"],
            }
            report.append(report_item)

        for date in date_range:
            if str(date) not in {str(item["ordering"]) for item in report}:
                label = get_label_for_range_view(date, range_view)
                report.append(
                    {
                        "ordering": date,
                        "date": label,
                        "total_order": 0,
                        "total_customer": 0,
                        "total_new_member_customer": 0,
                        "total_old_member_customer": 0,
                        "total_no_member_customer": 0,
                    }
                )

        return sorted(report, key=lambda x: x["ordering"])


class AggregateReport(serializers.Serializer):
    new_customer = CharField(
        label="จำนวนลูกค้าใหม่",
        suffix="คน",
        color=Colors.GREEN,
    )
    old_customer = CharField(
        label="จำนวนลูกค้าเก่าที่กลับมาซื้อซ้ำ",
        suffix="คน",
        color=Colors.GREEN,
    )
    no_customer_orders = CharField(
        label="ออเดอร์ที่ไม่มีข้อมูลลูกค้า",
        suffix="คน",
        color=Colors.RED,
    )
    total_customer = CharField(
        label="จำนวนลูกค้าทั้งหมด",
        suffix="คน",
        color=Colors.BLUE,
    )
    total_orders = CharField(
        label="จำนวนออเดอร์ทั้งหมด",
        suffix="ออเดอร์",
        color=Colors.BLUE,
    )


class AnalyticDailySummaryCustomer(ReportViewSet):
    permission_classes = [ReportAnalyticDailyCustomerPermission]
    queryset = Order.objects.all()
    filter_serializer = CustomerFilter
    aggregate_serializer = AggregateReport

    def get_aggregate_logic(self, queryset, context):
        filter = context.get("filter", {})
        order_datetime = filter.get("order_datetime")
        start_date = order_datetime.get("lower")
        queryset = queryset.exclude(order_status=Order.CANCELLED)

        new_customer = (
            queryset.filter(customer__create_at__gte=start_date)
            .distinct("customer")
            .count()
        )
        no_customer_orders = queryset.filter(customer__isnull=True).count()

        all_customers = queryset.filter(customer__isnull=False).values_list(
            "customer", flat=True
        )

        db_old_customer = (
            queryset.filter(customer__create_at__lt=start_date)
            .distinct("customer")
            .values_list("customer", flat=True)
        )

        # count customer that buy more than one time
        repeat_customer_count = 0
        map_total_customer = {c: 1 for c in db_old_customer}
        for customer in all_customers:
            if customer not in map_total_customer:
                map_total_customer[customer] = 1
            else:
                map_total_customer[customer] += 1

        # filter customer that buy more than one time
        for count in map_total_customer.values():
            if count > 1:
                repeat_customer_count += 1

        return {
            "new_customer": new_customer,
            "old_customer": repeat_customer_count,
            "no_customer_orders": no_customer_orders,
            "total_customer": len(map_total_customer.keys()),
            "total_orders": queryset.count(),
        }


class PurchaseFrequencyReport(ReportViewSet):
    permission_classes = [
        ReportPurchaseFrequencyPermission | ReportAnalyticDailyCustomerPermission
    ]
    filter_serializer = OrderCompareFilterSerializer
    use_compare_report = True
    merge_main_field = "purchase_frequency"
    filter_layouts = COMPARE_ORDER_LAYOUTS

    report_headers = [
        ReportHeader(
            title="จำนวนความถี่การซื้อ",
            key="purchase_frequency",
        ),
        ReportHeader(
            title="ความถี่",
            key="count",
            type="number",
        ),
    ]
    queryset = Order.objects.all()

    def get_report_logic(self, queryset, context) -> list:
        queryset = (
            queryset.filter(customer__isnull=False)
            .exclude(order_status="CANCELLED")
            .values("customer_id")
            .annotate(purchase_frequency=Count("id"))
        )

        sum_frequency = {}
        for item in queryset:
            if item["purchase_frequency"] not in sum_frequency:
                sum_frequency[item["purchase_frequency"]] = 0
            sum_frequency[item["purchase_frequency"]] += 1

        report_result = [
            {"purchase_frequency": key, "count": value}
            for key, value in sum_frequency.items()
        ]

        report_result.sort(key=lambda x: x["purchase_frequency"])

        outputs = []
        for item in report_result:
            item["purchase_frequency"] = f"{item['purchase_frequency']} ครั้ง"
            outputs.append(item)

        return outputs
